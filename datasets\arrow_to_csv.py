#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arrow文件转CSV格式工具
支持单个文件转换和批量转换
"""

import os
import sys
import argparse
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datasets import Dataset, DatasetDict, load_from_disk
from pathlib import Path

class ArrowToCSVConverter:
    def __init__(self, output_dir="./csv_datasets"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def convert_arrow_file(self, arrow_file_path, output_csv_path=None):
        """转换单个Arrow文件为CSV"""
        print(f"🔄 转换Arrow文件: {arrow_file_path}")
        
        try:
            # 读取Arrow文件
            table = pa.ipc.open_file(arrow_file_path).read_all()
            
            # 转换为pandas DataFrame
            df = table.to_pandas()
            
            # 生成输出文件名
            if output_csv_path is None:
                base_name = Path(arrow_file_path).stem
                output_csv_path = os.path.join(self.output_dir, f"{base_name}.csv")
            
            # 保存为CSV
            df.to_csv(output_csv_path, index=False, encoding='utf-8')
            
            print(f"✅ 转换成功: {output_csv_path}")
            print(f"   数据形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
            
            return output_csv_path
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return None
    
    def convert_dataset_dict(self, dataset_path, output_name=None):
        """转换Hugging Face数据集目录为CSV文件"""
        print(f"🔄 转换数据集目录: {dataset_path}")
        
        try:
            # 加载数据集
            dataset = load_from_disk(dataset_path)
            
            # 生成输出目录名
            if output_name is None:
                output_name = Path(dataset_path).name
            
            output_dataset_dir = os.path.join(self.output_dir, output_name)
            os.makedirs(output_dataset_dir, exist_ok=True)
            
            if isinstance(dataset, DatasetDict):
                # 处理多个分割的数据集
                print(f"📊 发现数据集分割: {list(dataset.keys())}")
                
                for split_name, split_dataset in dataset.items():
                    csv_file = os.path.join(output_dataset_dir, f"{split_name}.csv")
                    df = split_dataset.to_pandas()
                    df.to_csv(csv_file, index=False, encoding='utf-8')
                    
                    print(f"✅ {split_name}: {csv_file}")
                    print(f"   数据形状: {df.shape}")
                    
            elif isinstance(dataset, Dataset):
                # 处理单个数据集
                csv_file = os.path.join(output_dataset_dir, "data.csv")
                df = dataset.to_pandas()
                df.to_csv(csv_file, index=False, encoding='utf-8')
                
                print(f"✅ 数据集: {csv_file}")
                print(f"   数据形状: {df.shape}")
            
            print(f"🎉 数据集转换完成: {output_dataset_dir}")
            return output_dataset_dir
            
        except Exception as e:
            print(f"❌ 数据集转换失败: {e}")
            return None
    
    def convert_parquet_file(self, parquet_file_path, output_csv_path=None):
        """转换Parquet文件为CSV"""
        print(f"🔄 转换Parquet文件: {parquet_file_path}")
        
        try:
            # 读取Parquet文件
            df = pd.read_parquet(parquet_file_path)
            
            # 生成输出文件名
            if output_csv_path is None:
                base_name = Path(parquet_file_path).stem
                output_csv_path = os.path.join(self.output_dir, f"{base_name}.csv")
            
            # 保存为CSV
            df.to_csv(output_csv_path, index=False, encoding='utf-8')
            
            print(f"✅ 转换成功: {output_csv_path}")
            print(f"   数据形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
            
            return output_csv_path
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return None
    
    def batch_convert_directory(self, input_dir):
        """批量转换目录中的所有Arrow/Parquet文件"""
        print(f"🔄 批量转换目录: {input_dir}")
        
        converted_files = []
        
        # 遍历目录查找文件
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                if file_ext == '.arrow':
                    result = self.convert_arrow_file(file_path)
                    if result:
                        converted_files.append(result)
                        
                elif file_ext == '.parquet':
                    result = self.convert_parquet_file(file_path)
                    if result:
                        converted_files.append(result)
        
        # 检查是否有数据集目录
        for item in os.listdir(input_dir):
            item_path = os.path.join(input_dir, item)
            if os.path.isdir(item_path):
                # 检查是否是Hugging Face数据集目录
                if any(f in os.listdir(item_path) for f in ['dataset_info.json', 'state.json', 'dataset_dict.json']):
                    result = self.convert_dataset_dict(item_path)
                    if result:
                        converted_files.append(result)
        
        print(f"🎉 批量转换完成，共转换 {len(converted_files)} 个文件/数据集")
        return converted_files
    
    def show_preview(self, csv_file_path, rows=5):
        """预览CSV文件内容"""
        try:
            df = pd.read_csv(csv_file_path)
            print(f"\n📋 文件预览: {csv_file_path}")
            print(f"数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            print("\n前几行数据:")
            print(df.head(rows).to_string())
            
        except Exception as e:
            print(f"❌ 预览失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Arrow/Parquet文件转CSV工具')
    parser.add_argument('input_path', help='输入文件或目录路径')
    parser.add_argument('--output-dir', default='./csv_datasets', help='输出目录')
    parser.add_argument('--output-file', help='输出文件名（仅用于单文件转换）')
    parser.add_argument('--preview', action='store_true', help='转换后预览文件内容')
    parser.add_argument('--batch', action='store_true', help='批量转换目录中的所有文件')
    
    args = parser.parse_args()
    
    converter = ArrowToCSVConverter(args.output_dir)
    
    if os.path.isfile(args.input_path):
        # 单文件转换
        file_ext = Path(args.input_path).suffix.lower()
        
        if file_ext == '.arrow':
            result = converter.convert_arrow_file(args.input_path, args.output_file)
        elif file_ext == '.parquet':
            result = converter.convert_parquet_file(args.input_path, args.output_file)
        else:
            print(f"❌ 不支持的文件格式: {file_ext}")
            sys.exit(1)
        
        if result and args.preview:
            converter.show_preview(result)
            
    elif os.path.isdir(args.input_path):
        # 目录转换
        if args.batch:
            converter.batch_convert_directory(args.input_path)
        else:
            # 尝试作为数据集目录转换
            result = converter.convert_dataset_dict(args.input_path)
            if not result:
                print("❌ 不是有效的数据集目录，尝试使用 --batch 参数进行批量转换")
                sys.exit(1)
    else:
        print(f"❌ 路径不存在: {args.input_path}")
        sys.exit(1)

if __name__ == "__main__":
    # 如果没有命令行参数，提供交互式界面
    if len(sys.argv) == 1:
        print("🔄 Arrow/Parquet转CSV工具")
        print("=" * 40)
        
        input_path = input("请输入文件或目录路径: ").strip()
        if not input_path:
            print("❌ 路径不能为空")
            sys.exit(1)
        
        converter = ArrowToCSVConverter()
        
        if os.path.isfile(input_path):
            file_ext = Path(input_path).suffix.lower()
            if file_ext == '.arrow':
                result = converter.convert_arrow_file(input_path)
            elif file_ext == '.parquet':
                result = converter.convert_parquet_file(input_path)
            else:
                print(f"❌ 不支持的文件格式: {file_ext}")
                sys.exit(1)
                
            if result:
                preview = input("是否预览转换结果？(y/n): ").strip().lower()
                if preview == 'y':
                    converter.show_preview(result)
                    
        elif os.path.isdir(input_path):
            choice = input("选择转换模式:\n1. 数据集目录转换\n2. 批量文件转换\n请选择 (1/2): ").strip()
            
            if choice == '1':
                converter.convert_dataset_dict(input_path)
            elif choice == '2':
                converter.batch_convert_directory(input_path)
            else:
                print("❌ 无效选择")
        else:
            print(f"❌ 路径不存在: {input_path}")
    else:
        main()
