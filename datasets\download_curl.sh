#!/bin/bash
# 使用curl下载deepmind/code_contests数据集
# 适用于网络不稳定的情况，支持断点续传

REPO_ID="deepmind/code_contests"
SAVE_DIR="./downloaded_datasets/deepmind_code_contests"
BASE_URL="https://huggingface.co/datasets/${REPO_ID}/resolve/main"

# 创建保存目录
mkdir -p "$SAVE_DIR"
cd "$SAVE_DIR"

echo "🚀 开始下载 $REPO_ID 数据集..."
echo "保存到: $(pwd)"
echo "----------------------------------------"

# 下载基本信息文件
echo "📄 下载基本信息文件..."
curl -L -C - -o "README.md" "${BASE_URL}/README.md"
curl -L -C - -o "dataset_infos.json" "${BASE_URL}/dataset_infos.json"

# 下载数据文件（根据实际的文件结构调整）
echo "📦 下载数据文件..."

# 对于code_contests数据集，通常有以下文件结构
# 你可能需要根据实际情况调整这些文件名
DATA_FILES=(
    "data/train-00000-of-00039.parquet"
    "data/train-00001-of-00039.parquet"
    "data/train-00002-of-00039.parquet"
    "data/train-00003-of-00039.parquet"
    "data/train-00004-of-00039.parquet"
    "data/train-00005-of-00039.parquet"
    "data/train-00006-of-00039.parquet"
    "data/train-00007-of-00039.parquet"
    "data/train-00008-of-00039.parquet"
    "data/train-00009-of-00039.parquet"
    "data/train-00010-of-00039.parquet"
    "data/train-00011-of-00039.parquet"
    "data/train-00012-of-00039.parquet"
    "data/train-00013-of-00039.parquet"
    "data/train-00014-of-00039.parquet"
    "data/train-00015-of-00039.parquet"
    "data/train-00016-of-00039.parquet"
    "data/train-00017-of-00039.parquet"
    "data/train-00018-of-00039.parquet"
    "data/train-00019-of-00039.parquet"
    "data/train-00020-of-00039.parquet"
    "data/train-00021-of-00039.parquet"
    "data/train-00022-of-00039.parquet"
    "data/train-00023-of-00039.parquet"
    "data/train-00024-of-00039.parquet"
    "data/train-00025-of-00039.parquet"
    "data/train-00026-of-00039.parquet"
    "data/train-00027-of-00039.parquet"
    "data/train-00028-of-00039.parquet"
    "data/train-00029-of-00039.parquet"
    "data/train-00030-of-00039.parquet"
    "data/train-00031-of-00039.parquet"
    "data/train-00032-of-00039.parquet"
    "data/train-00033-of-00039.parquet"
    "data/train-00034-of-00039.parquet"
    "data/train-00035-of-00039.parquet"
    "data/train-00036-of-00039.parquet"
    "data/train-00037-of-00039.parquet"
    "data/train-00038-of-00039.parquet"
)

# 创建data目录
mkdir -p data

# 下载每个数据文件
for file in "${DATA_FILES[@]}"; do
    echo "下载: $file"
    curl -L -C - --retry 3 --retry-delay 5 -o "$file" "${BASE_URL}/$file"
    
    # 检查下载是否成功
    if [ $? -eq 0 ]; then
        echo "✅ 成功: $file"
    else
        echo "❌ 失败: $file"
    fi
    
    # 短暂暂停避免请求过于频繁
    sleep 2
done

echo "✨ 下载完成!"
echo "数据集保存在: $(pwd)"
ls -la
