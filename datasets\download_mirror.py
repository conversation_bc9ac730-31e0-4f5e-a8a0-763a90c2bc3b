#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用镜像站点下载Hugging Face数据集
适用于无法直接访问Hugging Face的情况
"""

import os
import requests
import json
from urllib.parse import urljoin
import time

def download_from_mirror(repo_id, save_path, mirror_url="https://hf-mirror.com"):
    """
    从镜像站点下载数据集
    
    参数:
    repo_id (str): 数据集仓库ID
    save_path (str): 保存路径
    mirror_url (str): 镜像站点URL
    """
    print(f"使用镜像站点下载: {mirror_url}")
    print(f"数据集: {repo_id}")
    
    # 创建保存目录
    repo_name = repo_id.replace('/', '_')
    target_path = os.path.join(save_path, repo_name)
    if not os.path.exists(target_path):
        os.makedirs(target_path)
        print(f"创建目录: {target_path}")
    
    # 构建API URL
    api_url = f"{mirror_url}/api/datasets/{repo_id}"
    
    try:
        # 获取数据集信息
        print("获取数据集信息...")
        response = requests.get(api_url, timeout=30)
        
        if response.status_code == 200:
            dataset_info = response.json()
            print("✅ 成功获取数据集信息")
            
            # 保存数据集信息
            info_file = os.path.join(target_path, "dataset_info.json")
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, indent=2, ensure_ascii=False)
            print(f"数据集信息已保存到: {info_file}")
            
        else:
            print(f"⚠️  无法获取数据集信息: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"获取数据集信息时出错: {e}")
    
    # 尝试下载主要文件
    files_to_try = [
        "README.md",
        "dataset_infos.json",
        "data/train.parquet",
        "data/test.parquet",
        "data/validation.parquet",
    ]
    
    base_download_url = f"{mirror_url}/datasets/{repo_id}/resolve/main/"
    
    for file_path in files_to_try:
        download_file_from_url(base_download_url + file_path, 
                             os.path.join(target_path, os.path.basename(file_path)))
    
    return target_path

def download_file_from_url(url, local_path, chunk_size=8192):
    """
    从URL下载文件，支持断点续传
    """
    try:
        print(f"尝试下载: {os.path.basename(local_path)}")
        
        # 检查是否已存在部分下载的文件
        resume_header = {}
        if os.path.exists(local_path):
            resume_header['Range'] = f'bytes={os.path.getsize(local_path)}-'
            mode = 'ab'
        else:
            mode = 'wb'
        
        response = requests.get(url, headers=resume_header, stream=True, timeout=30)
        
        if response.status_code in [200, 206]:  # 200: 完整下载, 206: 部分内容
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(local_path, mode) as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # 显示进度
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
            
            print(f"\n✅ 成功下载: {os.path.basename(local_path)}")
            return True
            
        else:
            print(f"⚠️  跳过: {os.path.basename(local_path)} (HTTP {response.status_code})")
            return False
            
    except Exception as e:
        print(f"\n❌ 下载 {os.path.basename(local_path)} 时出错: {e}")
        return False

def download_with_manual_urls(repo_id, save_path):
    """
    手动指定文件URL进行下载（适用于已知具体文件的情况）
    """
    print("使用手动URL列表下载...")
    
    # 对于 deepmind/code_contests，这些是一些可能的文件
    # 你可能需要根据实际情况调整这些URL
    manual_urls = [
        f"https://huggingface.co/datasets/{repo_id}/resolve/main/README.md",
        f"https://huggingface.co/datasets/{repo_id}/resolve/main/dataset_infos.json",
        # 添加更多已知的文件URL
    ]
    
    repo_name = repo_id.replace('/', '_')
    target_path = os.path.join(save_path, repo_name)
    if not os.path.exists(target_path):
        os.makedirs(target_path)
    
    for url in manual_urls:
        filename = os.path.basename(url.split('/')[-1])
        local_path = os.path.join(target_path, filename)
        download_file_from_url(url, local_path)
        time.sleep(1)  # 避免请求过于频繁

if __name__ == "__main__":
    repo_id = "deepmind/code_contests"
    save_path = "./downloaded_datasets"
    
    print("🚀 使用镜像站点下载数据集...")
    print(f"数据集: {repo_id}")
    print(f"保存路径: {save_path}")
    print("-" * 50)
    
    # 尝试不同的镜像站点
    mirrors = [
        "https://hf-mirror.com",
        "https://huggingface.co",  # 原站点
    ]
    
    success = False
    for mirror in mirrors:
        print(f"\n尝试镜像站点: {mirror}")
        try:
            result = download_from_mirror(repo_id, save_path, mirror)
            if result:
                success = True
                break
        except Exception as e:
            print(f"镜像站点 {mirror} 失败: {e}")
            continue
    
    if not success:
        print("\n所有镜像站点都失败，尝试手动URL下载...")
        download_with_manual_urls(repo_id, save_path)
    
    print("\n✨ 下载尝试完成!")
