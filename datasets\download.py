#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用数据集下载脚本
支持三种下载方法：Git、Hugging Face、HF-Mirror
适配所有Hugging Face数据集
"""

import os
import sys
import subprocess
import argparse
from datasets import load_dataset

class DatasetDownloader:
    def __init__(self, repo_id, save_path="./downloaded_datasets"):
        self.repo_id = repo_id
        self.save_path = save_path
        self.repo_name = repo_id.replace('/', '_')
        self.target_path = os.path.join(save_path, self.repo_name)

    def method_git(self):
        """方法1: 使用Git克隆"""
        print(f"🔄 方法1: Git克隆 {self.repo_id}")

        git_url = f"https://huggingface.co/datasets/{self.repo_id}"

        try:
            if os.path.exists(self.target_path):
                print(f"目录已存在，删除: {self.target_path}")
                import shutil
                shutil.rmtree(self.target_path)

            cmd = ["git", "clone", git_url, self.target_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                print(f"✅ Git下载成功: {self.target_path}")
                return True
            else:
                print(f"❌ Git下载失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("❌ Git下载超时")
            return False
        except FileNotFoundError:
            print("❌ 未找到git命令")
            return False
        except Exception as e:
            print(f"❌ Git下载出错: {e}")
            return False

    def method_huggingface(self, name=None, split=None):
        """方法2: 使用Hugging Face datasets库"""
        print(f"🔄 方法2: Hugging Face datasets {self.repo_id}")

        try:
            dataset = load_dataset(
                path=self.repo_id,
                name=name,
                split=split,
                trust_remote_code=True
            )

            if not os.path.exists(self.save_path):
                os.makedirs(self.save_path)

            dataset.save_to_disk(self.target_path)
            print(f"✅ Hugging Face下载成功: {self.target_path}")
            return True

        except Exception as e:
            print(f"❌ Hugging Face下载失败: {e}")
            return False

    def method_mirror(self, mirror_url="https://hf-mirror.com"):
        """方法3: 使用镜像站点"""
        print(f"🔄 方法3: 镜像站点 {mirror_url}")

        # 设置环境变量使用镜像
        os.environ['HF_ENDPOINT'] = mirror_url

        try:
            dataset = load_dataset(
                path=self.repo_id,
                trust_remote_code=True
            )

            if not os.path.exists(self.save_path):
                os.makedirs(self.save_path)

            dataset.save_to_disk(self.target_path)
            print(f"✅ 镜像下载成功: {self.target_path}")
            return True

        except Exception as e:
            print(f"❌ 镜像下载失败: {e}")
            return False
        finally:
            # 恢复环境变量
            if 'HF_ENDPOINT' in os.environ:
                del os.environ['HF_ENDPOINT']

    def download(self, methods=['git', 'huggingface', 'mirror'], **kwargs):
        """尝试所有方法下载数据集"""
        print(f"🚀 开始下载数据集: {self.repo_id}")
        print(f"保存路径: {self.target_path}")
        print("-" * 50)

        for method in methods:
            try:
                if method == 'git':
                    if self.method_git():
                        return True
                elif method == 'huggingface':
                    if self.method_huggingface(**kwargs):
                        return True
                elif method == 'mirror':
                    if self.method_mirror():
                        return True
                else:
                    print(f"⚠️  未知方法: {method}")

            except KeyboardInterrupt:
                print("\n⚠️  用户中断下载")
                return False
            except Exception as e:
                print(f"❌ 方法 {method} 出现异常: {e}")
                continue

        print("❌ 所有下载方法都失败了")
        return False

def main():
    parser = argparse.ArgumentParser(description='通用数据集下载工具')
    parser.add_argument('repo_id', help='数据集仓库ID，如: deepmind/code_contests')
    parser.add_argument('--save-path', default='./downloaded_datasets', help='保存路径')
    parser.add_argument('--methods', nargs='+', default=['git', 'huggingface', 'mirror'],
                       choices=['git', 'huggingface', 'mirror'], help='下载方法')
    parser.add_argument('--name', help='数据集配置名称')
    parser.add_argument('--split', help='数据集分割 (train/test/validation)')

    args = parser.parse_args()

    downloader = DatasetDownloader(args.repo_id, args.save_path)
    success = downloader.download(
        methods=args.methods,
        name=args.name,
        split=args.split
    )

    if success:
        print(f"\n✨ 下载完成! 数据集保存在: {downloader.target_path}")
    else:
        print(f"\n💥 下载失败!")
        sys.exit(1)

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认配置
    if len(sys.argv) == 1:
        # 默认下载配置
        repo_id = "deepmind/code_contests"
        downloader = DatasetDownloader(repo_id)
        downloader.download()
    else:
        main()