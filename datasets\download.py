# -*- coding: utf-8 -*-
# 确保你已经安装了 'datasets' 库.
# 如果没有安装，可以在你的终端或命令行中运行:
# pip install datasets

from datasets import load_dataset
import os

def download_huggingface_dataset(repo_id, save_path, name=None, split=None):
    """
    从 Hugging Face Hub 下载数据集并将其保存到本地磁盘。

    参数:
    repo_id (str): 数据集在 Hugging Face Hub 上的仓库ID (例如: 'squad', 'cifar10').
    save_path (str): 你想要将数据集保存到的本地文件夹路径。
    name (str, optional): 数据集的特定配置或子集名称 (例如: 'plain_text' for 'wikitext' dataset). 默认为 None.
    split (str, optional): 下载特定的数据部分 (例如: 'train', 'test', 'validation'). 默认为下载所有部分.
    """
    print(f"开始下载数据集: {repo_id}...")
    
    try:
        # 使用 load_dataset 函数从 Hugging Face Hub 加载数据集
        # stream=True 可以让你在下载完成前就访问数据，但我们这里为了完整保存，设置为False
        dataset = load_dataset(path=repo_id, name=name, split=split, trust_remote_code=True)
        
        print("数据集加载成功!")
        print("数据集信息:")
        print(dataset)

        # 确保保存路径存在
        if not os.path.exists(save_path):
            os.makedirs(save_path)
            print(f"创建文件夹: {save_path}")

        # 将数据集保存到磁盘
        # 这会将数据集以 Arrow 格式保存在指定路径
        save_full_path = os.path.join(save_path, repo_id.replace('/', '_')) # 创建一个有效的文件名
        dataset.save_to_disk(save_full_path)
        
        print(f"数据集已成功保存到: {save_full_path}")
        return save_full_path
        
    except Exception as e:
        print(f"下载或保存数据集时出错: {e}")
        print("请检查以下几点:")
        print(f"1. 数据集仓库ID '{repo_id}' 是否正确?")
        print("2. 你的网络连接是否正常?")
        print(f"3. 如果数据集需要，是否提供了正确的 'name' 参数?")
        return None

# --- 使用示例 ---

if __name__ == "__main__":
    cifar_repo_id = "thu-coai/SafetyBench"
    cifar_save_path = "./downloaded_datasets"
    download_huggingface_dataset(repo_id=cifar_repo_id, save_path=cifar_save_path, split='test')


