#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Git直接从Hugging Face下载数据集
适用于网络连接不稳定或无法访问Hugging Face Hub的情况
"""

import os
import subprocess
import sys

def download_with_git(repo_id, save_path):
    """
    使用Git克隆方式下载Hugging Face数据集
    
    参数:
    repo_id (str): 数据集仓库ID，如 'deepmind/code_contests'
    save_path (str): 保存路径
    """
    # 构建Git仓库URL
    git_url = f"https://huggingface.co/datasets/{repo_id}"
    
    # 创建保存目录
    if not os.path.exists(save_path):
        os.makedirs(save_path)
        print(f"创建目录: {save_path}")
    
    # 构建目标路径
    repo_name = repo_id.replace('/', '_')
    target_path = os.path.join(save_path, repo_name)
    
    print(f"开始使用Git下载数据集: {repo_id}")
    print(f"Git URL: {git_url}")
    print(f"保存到: {target_path}")
    
    try:
        # 使用git clone下载
        cmd = ["git", "clone", git_url, target_path]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据集下载成功!")
            print(f"数据集已保存到: {target_path}")
            
            # 显示下载的文件
            print("\n📁 下载的文件:")
            for root, dirs, files in os.walk(target_path):
                level = root.replace(target_path, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:10]:  # 只显示前10个文件
                    print(f"{subindent}{file}")
                if len(files) > 10:
                    print(f"{subindent}... 还有 {len(files) - 10} 个文件")
                    
            return target_path
        else:
            print("❌ Git克隆失败:")
            print(result.stderr)
            return None
            
    except FileNotFoundError:
        print("❌ 错误: 未找到git命令")
        print("请确保已安装Git: https://git-scm.com/downloads")
        return None
    except Exception as e:
        print(f"❌ 下载过程中出错: {e}")
        return None

def download_with_wget(repo_id, save_path):
    """
    使用wget下载特定文件（适用于只需要部分文件的情况）
    """
    print(f"使用wget下载 {repo_id} 的主要文件...")
    
    # 创建保存目录
    repo_name = repo_id.replace('/', '_')
    target_path = os.path.join(save_path, repo_name)
    if not os.path.exists(target_path):
        os.makedirs(target_path)
    
    # 常见的数据集文件
    files_to_download = [
        "README.md",
        "dataset_infos.json",
        "data/train-00000-of-00001.parquet",  # 示例文件路径
    ]
    
    base_url = f"https://huggingface.co/datasets/{repo_id}/resolve/main/"
    
    for file_path in files_to_download:
        url = base_url + file_path
        local_file = os.path.join(target_path, os.path.basename(file_path))
        
        try:
            cmd = ["wget", "-O", local_file, url]
            print(f"下载: {file_path}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 成功下载: {file_path}")
            else:
                print(f"⚠️  跳过: {file_path} (可能不存在)")
                
        except FileNotFoundError:
            print("❌ 错误: 未找到wget命令")
            print("在Windows上可以使用Git Bash或安装wget")
            break
        except Exception as e:
            print(f"下载 {file_path} 时出错: {e}")

if __name__ == "__main__":
    # 配置
    repo_id = "deepmind/code_contests"
    save_path = "./downloaded_datasets"
    
    print("🚀 开始下载数据集...")
    print(f"数据集: {repo_id}")
    print(f"保存路径: {save_path}")
    print("-" * 50)
    
    # 方法1: 使用Git克隆
    result = download_with_git(repo_id, save_path)
    
    if result is None:
        print("\n尝试备用方法...")
        # 方法2: 使用wget下载主要文件
        download_with_wget(repo_id, save_path)
    
    print("\n✨ 下载完成!")
