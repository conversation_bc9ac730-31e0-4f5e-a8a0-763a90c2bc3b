#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接问题诊断和修复工具
"""

import os
import subprocess
import requests
import urllib.request
from urllib.error import URLError

def check_network_status():
    """检查网络连接状态"""
    print("🔍 检查网络连接状态...")
    
    # 检查基本网络连接
    test_urls = [
        "https://www.baidu.com",
        "https://www.google.com", 
        "https://huggingface.co",
        "https://hf-mirror.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url}: {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")

def check_proxy_settings():
    """检查代理设置"""
    print("\n🔍 检查代理设置...")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"🔧 {var}: {value}")
        else:
            print(f"⚪ {var}: 未设置")

def clear_proxy():
    """清除代理设置"""
    print("\n🧹 清除代理设置...")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"🗑️  已清除: {var}")

def set_proxy(proxy_url):
    """设置代理"""
    print(f"\n🔧 设置代理: {proxy_url}")
    
    os.environ['HTTP_PROXY'] = proxy_url
    os.environ['HTTPS_PROXY'] = proxy_url
    os.environ['http_proxy'] = proxy_url
    os.environ['https_proxy'] = proxy_url
    
    print("✅ 代理设置完成")

def configure_git_proxy(proxy_url=None):
    """配置Git代理"""
    print("\n🔧 配置Git代理...")
    
    if proxy_url:
        try:
            subprocess.run(['git', 'config', '--global', 'http.proxy', proxy_url], check=True)
            subprocess.run(['git', 'config', '--global', 'https.proxy', proxy_url], check=True)
            print(f"✅ Git代理设置为: {proxy_url}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Git代理设置失败: {e}")
    else:
        try:
            subprocess.run(['git', 'config', '--global', '--unset', 'http.proxy'], check=True)
            subprocess.run(['git', 'config', '--global', '--unset', 'https.proxy'], check=True)
            print("✅ Git代理已清除")
        except subprocess.CalledProcessError:
            print("⚪ Git代理未设置或已清除")

def configure_pip_proxy(proxy_url=None):
    """配置pip代理"""
    print("\n🔧 配置pip代理...")
    
    pip_conf_dir = os.path.expanduser("~/.pip")
    pip_conf_file = os.path.join(pip_conf_dir, "pip.conf")
    
    if proxy_url:
        os.makedirs(pip_conf_dir, exist_ok=True)
        with open(pip_conf_file, 'w') as f:
            f.write(f"""[global]
proxy = {proxy_url}
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
""")
        print(f"✅ pip代理配置已写入: {pip_conf_file}")
    else:
        if os.path.exists(pip_conf_file):
            os.remove(pip_conf_file)
            print("✅ pip代理配置已清除")

def test_huggingface_connection():
    """测试Hugging Face连接"""
    print("\n🧪 测试Hugging Face连接...")
    
    test_urls = [
        "https://huggingface.co/api/models",
        "https://hf-mirror.com/api/models"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=30)
            print(f"✅ {url}: {response.status_code}")
            return True
        except Exception as e:
            print(f"❌ {url}: {e}")
    
    return False

def main():
    print("🛠️  网络连接问题诊断工具")
    print("=" * 50)
    
    # 检查当前状态
    check_network_status()
    check_proxy_settings()
    
    print("\n" + "=" * 50)
    print("🔧 修复选项:")
    print("1. 清除所有代理设置")
    print("2. 设置代理 (需要输入代理地址)")
    print("3. 只清除Git代理")
    print("4. 测试Hugging Face连接")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            clear_proxy()
            configure_git_proxy()
            configure_pip_proxy()
            print("✅ 所有代理设置已清除")
            
        elif choice == '2':
            proxy = input("请输入代理地址 (格式: http://host:port): ").strip()
            if proxy:
                set_proxy(proxy)
                configure_git_proxy(proxy)
                configure_pip_proxy(proxy)
            
        elif choice == '3':
            configure_git_proxy()
            
        elif choice == '4':
            test_huggingface_connection()
            
        elif choice == '5':
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
